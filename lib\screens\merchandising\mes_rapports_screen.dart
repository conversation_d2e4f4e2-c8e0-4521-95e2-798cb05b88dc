import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/rapport_merchandising.dart';
import '../../providers/rapport_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'creer_rapport_screen.dart';

class MesRapportsScreen extends StatefulWidget {
  const MesRapportsScreen({super.key});

  @override
  State<MesRapportsScreen> createState() => _MesRapportsScreenState();
}

class _MesRapportsScreenState extends State<MesRapportsScreen> {
  String _filtreStatut = 'tous';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerRapports();
    });
  }

  void _chargerRapports() {
    final provider = Provider.of<RapportProvider>(context, listen: false);
    // TODO: Get actual merchandiser ID from auth provider
    provider.chargerRapportsParMerchandiser('merchandiser_123');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: MediaQuery.of(context).size.width < 600 ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            leading: Builder(
              builder:
                  (context) => IconButton(
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Color(0xFF1E293B),
                      size: 28,
                    ),
                    tooltip: 'Retour',
                    onPressed: () => Navigator.of(context).pop(),
                  ),
            ),
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.refresh,
                  color: Color(0xFF1E293B),
                  size: 28,
                ),
                tooltip: 'Actualiser',
                onPressed: _chargerRapports,
              ),
              const SizedBox(width: 8),
            ],
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
              title: Row(
                children: [
                  VitaBrosseLogo(
                    height: MediaQuery.of(context).size.width < 600 ? 24 : 28,
                    showText: false,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Mes rapports',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize:
                                MediaQuery.of(context).size.width < 600
                                    ? 18
                                    : 20,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Gestion des rapports',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF10B981).withValues(alpha: 0.05),
                      const Color(0xFF059669).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Column(
              children: [
                // Filtres
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.filter_list,
                          color: Color(0xFF10B981),
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Filtrer par statut:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color(0xFFE2E8F0),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: DropdownButton<String>(
                              value: _filtreStatut,
                              isExpanded: true,
                              underline: const SizedBox(),
                              items: const [
                                DropdownMenuItem(
                                  value: 'tous',
                                  child: Text('Tous'),
                                ),
                                DropdownMenuItem(
                                  value: 'brouillon',
                                  child: Text('Brouillons'),
                                ),
                                DropdownMenuItem(
                                  value: 'envoye',
                                  child: Text('Envoyés'),
                                ),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _filtreStatut = value!;
                                });
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SliverToBoxAdapter(
            child: Consumer<RapportProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Padding(
                    padding: EdgeInsets.all(50),
                    child: Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF10B981),
                      ),
                    ),
                  );
                }

                final rapportsFiltres = _filtrerRapports(
                  provider.rapportsMerchandising,
                );

                if (rapportsFiltres.isEmpty) {
                  return Padding(
                    padding: const EdgeInsets.all(50),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.description_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucun rapport trouvé',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Créez votre premier rapport',
                            style: TextStyle(color: Colors.grey.shade500),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children:
                        rapportsFiltres
                            .map((rapport) => _buildRapportCard(rapport))
                            .toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreerRapportScreen()),
          ).then((_) => _chargerRapports());
        },
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Nouveau rapport'),
        heroTag: "add_rapport",
      ),
    );
  }

  List<RapportMerchandising> _filtrerRapports(
    List<RapportMerchandising> rapports,
  ) {
    if (_filtreStatut == 'tous') {
      return rapports;
    }
    return rapports
        .where((rapport) => rapport.statut == _filtreStatut)
        .toList();
  }

  Widget _buildRapportCard(RapportMerchandising rapport) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatutColor(
                      rapport.statut,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    rapport.statut == 'brouillon'
                        ? Icons.edit_note
                        : Icons.send,
                    color: _getStatutTextColor(rapport.statut),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        rapport.titre,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Créé le ${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatutColor(rapport.statut),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatutText(rapport.statut),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _getStatutTextColor(rapport.statut),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              rapport.observations.length > 100
                  ? '${rapport.observations.substring(0, 100)}...'
                  : rapport.observations,
              style: const TextStyle(fontSize: 14, color: Color(0xFF64748B)),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => _voirDetails(rapport),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.visibility,
                            size: 16,
                            color: Color(0xFF10B981),
                          ),
                          SizedBox(width: 4),
                          Text(
                            'Voir détails',
                            style: TextStyle(
                              color: Color(0xFF10B981),
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _onMenuSelected(value, rapport),
                  icon: const Icon(Icons.more_vert, color: Color(0xFF64748B)),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'voir',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 16),
                              SizedBox(width: 8),
                              Text('Voir détails'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'modifier',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'supprimer',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 16, color: Colors.red),
                              SizedBox(width: 8),
                              Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                ),
                if (rapport.statut == 'brouillon') ...[
                  const PopupMenuItem(
                    value: 'modifier',
                    child: Text('Modifier'),
                  ),
                  const PopupMenuItem(value: 'envoyer', child: Text('Envoyer')),
                ],
                const PopupMenuItem(
                  value: 'supprimer',
                  child: Text('Supprimer'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'envoye':
        return const Color(0xFF10B981).withValues(alpha: 0.1);
      case 'brouillon':
        return const Color(0xFFF59E0B).withValues(alpha: 0.1);
      default:
        return const Color(0xFF64748B).withValues(alpha: 0.1);
    }
  }

  Color _getStatutTextColor(String statut) {
    switch (statut) {
      case 'envoye':
        return const Color(0xFF10B981);
      case 'brouillon':
        return const Color(0xFFF59E0B);
      default:
        return const Color(0xFF64748B);
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'envoye':
        return 'Envoyé';
      case 'brouillon':
        return 'Brouillon';
      default:
        return 'Inconnu';
    }
  }

  void _onMenuSelected(String value, RapportMerchandising rapport) {
    switch (value) {
      case 'voir':
        _voirDetails(rapport);
        break;
      case 'modifier':
        _modifierRapport(rapport);
        break;
      case 'envoyer':
        _envoyerRapport(rapport);
        break;
      case 'supprimer':
        _confirmerSuppression(rapport);
        break;
    }
  }

  void _voirDetails(RapportMerchandising rapport) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(rapport.titre),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Statut: ${_getStatutText(rapport.statut)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Date: ${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Observations:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(rapport.observations),
                  if (rapport.recommandations != null) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'Recommandations:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(rapport.recommandations!),
                  ],
                  if (rapport.photos.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Photos: ${rapport.photos.length}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
              if (rapport.statut == 'brouillon')
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _modifierRapport(rapport);
                  },
                  child: const Text('Modifier'),
                ),
            ],
          ),
    );
  }

  void _modifierRapport(RapportMerchandising rapport) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreerRapportScreen(rapport: rapport),
      ),
    ).then((_) => _chargerRapports());
  }

  Future<void> _envoyerRapport(RapportMerchandising rapport) async {
    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);

      final rapportModifie = RapportMerchandising(
        id: rapport.id,
        missionId: rapport.missionId,
        titre: rapport.titre,
        observations: rapport.observations,
        recommandations: rapport.recommandations,
        dateCreation: rapport.dateCreation,
        dateModification: DateTime.now(),
        statut: 'envoye',
        photos: rapport.photos,
      );

      final success = await provider.modifierRapportMerchandising(
        rapportModifie,
      );

      if (success && mounted) {
        _chargerRapports();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Rapport envoyé avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _confirmerSuppression(RapportMerchandising rapport) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le rapport "${rapport.titre}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _supprimerRapport(rapport);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  Future<void> _supprimerRapport(RapportMerchandising rapport) async {
    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);
      final success = await provider.supprimerRapportMerchandising(rapport.id!);

      if (success && mounted) {
        _chargerRapports();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Rapport supprimé avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
