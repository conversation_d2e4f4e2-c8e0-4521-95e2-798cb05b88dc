import 'package:flutter/material.dart';
import '../models/rapport.dart';
import '../models/rapport_merchandising.dart';
import '../services/rapport_service.dart';

class RapportProvider with ChangeNotifier {
  final RapportService _rapportService = RapportService();

  List<Rapport> _rapports = [];
  List<Rapport> _rapportsBrouillon = [];
  List<RapportMerchandising> _rapportsMerchandising = [];
  bool _isLoading = false;
  String? _error;

  List<Rapport> get rapports => _rapports;
  List<Rapport> get rapportsBrouillon => _rapportsBrouillon;
  List<RapportMerchandising> get rapportsMerchandising =>
      _rapportsMerchandising;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les rapports
  Future<void> chargerRapports() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _rapports = await _rapportService.obtenirTousLesRapports();
    } catch (e) {
      _error = 'Erreur lors du chargement des rapports: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les rapports d'un merchandiser
  Future<void> chargerRapportsParMerchandiser(String merchandiserId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _rapportsMerchandising = await _rapportService
          .obtenirRapportsMerchandisingParMerchandiser(merchandiserId);
    } catch (e) {
      _error = 'Erreur lors du chargement des rapports: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les rapports pour un commercial
  Future<void> chargerRapportsParCommercial(String commercialId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _rapports = await _rapportService.obtenirRapportsParCommercial(
        commercialId,
      );
    } catch (e) {
      _error = 'Erreur lors du chargement des rapports: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les rapports en brouillon (merchandising)
  Future<void> chargerRapportsBrouillon(String merchandiserId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _rapportsMerchandising = await _rapportService
          .obtenirRapportsMerchandisingBrouillon(merchandiserId);
    } catch (e) {
      _error = 'Erreur lors du chargement des rapports en brouillon: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger tous les rapports de merchandising (pour les commerciaux)
  Future<void> chargerTousLesRapportsMerchandising() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _rapportsMerchandising =
          await _rapportService.obtenirTousLesRapportsMerchandising();
    } catch (e) {
      _error = 'Erreur lors du chargement des rapports de merchandising: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau rapport
  Future<bool> creerRapport(Rapport rapport) async {
    try {
      await _rapportService.creerRapport(rapport);
      // Recharger les rapports après création
      await chargerRapports();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du rapport: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour le statut d'un rapport
  Future<bool> mettreAJourStatut(String rapportId, String nouveauStatut) async {
    try {
      await _rapportService.mettreAJourStatut(rapportId, nouveauStatut);

      // Mettre à jour localement
      final index = _rapports.indexWhere((r) => r.id == rapportId);
      if (index != -1) {
        _rapports[index] = _rapports[index].copyWith(statut: nouveauStatut);
      }

      // Mettre à jour dans les brouillons
      final indexBrouillon = _rapportsBrouillon.indexWhere(
        (r) => r.id == rapportId,
      );
      if (indexBrouillon != -1) {
        if (nouveauStatut != 'brouillon') {
          _rapportsBrouillon.removeAt(indexBrouillon);
        } else {
          _rapportsBrouillon[indexBrouillon] =
              _rapportsBrouillon[indexBrouillon].copyWith(
                statut: nouveauStatut,
              );
        }
      }

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du statut: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour un rapport
  Future<bool> mettreAJourRapport(Rapport rapport) async {
    try {
      await _rapportService.mettreAJourRapport(rapport);

      // Mettre à jour localement
      final index = _rapports.indexWhere((r) => r.id == rapport.id);
      if (index != -1) {
        _rapports[index] = rapport;
      }

      // Mettre à jour dans les brouillons
      final indexBrouillon = _rapportsBrouillon.indexWhere(
        (r) => r.id == rapport.id,
      );
      if (indexBrouillon != -1) {
        _rapportsBrouillon[indexBrouillon] = rapport;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du rapport: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un rapport
  Future<bool> supprimerRapport(String rapportId) async {
    try {
      await _rapportService.supprimerRapport(rapportId);

      // Supprimer localement
      _rapports.removeWhere((r) => r.id == rapportId);
      _rapportsBrouillon.removeWhere((r) => r.id == rapportId);

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du rapport: $e';
      notifyListeners();
      return false;
    }
  }

  // Obtenir un rapport par ID
  Future<Rapport?> obtenirRapportParId(String rapportId) async {
    try {
      return await _rapportService.obtenirRapportParId(rapportId);
    } catch (e) {
      _error = 'Erreur lors de la récupération du rapport: $e';
      notifyListeners();
      return null;
    }
  }

  // Obtenir les rapports d'une mission
  Future<List<Rapport>> obtenirRapportsParMission(String missionId) async {
    try {
      return await _rapportService.obtenirRapportsParMission(missionId);
    } catch (e) {
      _error = 'Erreur lors de la récupération des rapports de la mission: $e';
      notifyListeners();
      return [];
    }
  }

  // Obtenir les rapports par statut
  Future<List<Rapport>> obtenirRapportsParStatut(
    String merchandiserId,
    String statut,
  ) async {
    try {
      return await _rapportService.obtenirRapportsParStatut(
        merchandiserId,
        statut,
      );
    } catch (e) {
      _error = 'Erreur lors de la récupération des rapports par statut: $e';
      notifyListeners();
      return [];
    }
  }

  // Ajouter un feedback du commercial
  Future<bool> ajouterFeedbackCommercial(
    String rapportId,
    String feedback,
  ) async {
    try {
      await _rapportService.ajouterFeedbackCommercial(rapportId, feedback);

      // Mettre à jour localement
      final index = _rapports.indexWhere((r) => r.id == rapportId);
      if (index != -1) {
        _rapports[index] = _rapports[index].copyWith(
          feedbackCommercial: feedback,
        );
      }

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du feedback: $e';
      notifyListeners();
      return false;
    }
  }

  // Envoyer un rapport (changer le statut de brouillon à envoyé)
  Future<bool> envoyerRapport(String rapportId) async {
    return await mettreAJourStatut(rapportId, 'envoye');
  }

  // Valider un rapport (commercial)
  Future<bool> validerRapport(String rapportId) async {
    return await mettreAJourStatut(rapportId, 'valide');
  }

  // Rejeter un rapport (commercial)
  Future<bool> rejeterRapport(String rapportId) async {
    return await mettreAJourStatut(rapportId, 'rejete');
  }

  // Filtrer les rapports par statut localement
  List<Rapport> filtrerParStatut(String statut) {
    return _rapports.where((rapport) => rapport.statut == statut).toList();
  }

  // Filtrer les rapports par date
  List<Rapport> filtrerParDate(DateTime dateDebut, DateTime dateFin) {
    return _rapports
        .where(
          (rapport) =>
              rapport.dateRapport.isAfter(dateDebut) &&
              rapport.dateRapport.isBefore(dateFin),
        )
        .toList();
  }

  // Obtenir les statistiques des rapports
  Map<String, int> get statistiquesRapports {
    final stats = <String, int>{};
    for (final rapport in _rapports) {
      stats[rapport.statut] = (stats[rapport.statut] ?? 0) + 1;
    }
    return stats;
  }

  // Générer un ID unique pour un rapport
  String genererIdRapport() {
    return _rapportService.genererIdRapport();
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Méthodes pour RapportMerchandising
  Future<bool> creerRapportMerchandising(RapportMerchandising rapport) async {
    try {
      // Simuler la création d'un rapport
      final nouveauRapport = rapport.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _rapportsMerchandising.add(nouveauRapport);
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du rapport: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> modifierRapportMerchandising(
    RapportMerchandising rapport,
  ) async {
    try {
      final index = _rapportsMerchandising.indexWhere(
        (r) => r.id == rapport.id,
      );
      if (index != -1) {
        _rapportsMerchandising[index] = rapport;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du rapport: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> supprimerRapportMerchandising(String id) async {
    try {
      _rapportsMerchandising.removeWhere((r) => r.id == id);
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du rapport: $e';
      notifyListeners();
      return false;
    }
  }
}
