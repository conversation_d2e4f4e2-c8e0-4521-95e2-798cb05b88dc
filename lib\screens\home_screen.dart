import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/firebase_client_provider.dart';
import '../providers/produit_provider.dart';
import '../providers/commande_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/vitabrosse_logo.dart';
import '../widgets/professional_ui_components.dart';
import '../widgets/revenue_chart.dart';
import 'auth/login_screen.dart';
import 'clients/clients_screen.dart';
import 'produits/produits_screen.dart';
import 'commandes/commandes_screen.dart';
import 'devis/devis_screen.dart';
import 'catalogue/catalogue_screen.dart';
import 'merchandising/merchandising_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const ClientsScreen(),
    const ProduitsScreen(),
    const CommandesScreen(),
    const DevisScreen(),
    const CatalogueScreen(),
    const MerchandisingScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Utiliser addPostFrameCallback pour éviter setState pendant build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
      _preloadStatistics();
    });
  }

  Future<void> _preloadStatistics() async {
    try {
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );
      final commandeProvider = Provider.of<CommandeProvider>(
        context,
        listen: false,
      );

      // Preload statistics to avoid UI loading issues
      await Future.wait([
        clientProvider.obtenirStatistiques(),
        commandeProvider.obtenirStatistiques(),
      ]);

      print('Statistics preloaded successfully');
    } catch (e) {
      print('Error preloading statistics: $e');
    }
  }

  Future<void> _chargerDonnees() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final produitProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final commandeProvider = Provider.of<CommandeProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      clientProvider.loadClients(),
      produitProvider.chargerProduits(),
      produitProvider.chargerCategories(),
      commandeProvider.chargerCommandes(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) {
            final previousIndex = _selectedIndex;
            setState(() {
              _selectedIndex = index;
            });

            // Only refresh clients if we're switching TO the clients tab from another tab
            if (index == 1 && previousIndex != 1) {
              // Clients tab
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  final provider = context.read<FirebaseClientProvider>();
                  // Only refresh if the list is empty or hasn't been loaded
                  if (provider.clients.isEmpty) {
                    provider.refreshClients();
                  }
                }
              });
            }
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard_outlined),
              activeIcon: Icon(Icons.dashboard),
              label: 'Tableau de bord',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'Clients',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'Produits',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.shopping_cart_outlined),
              activeIcon: Icon(Icons.shopping_cart),
              label: 'Commandes',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.description_outlined),
              activeIcon: Icon(Icons.description),
              label: 'Devis',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.library_books_outlined),
              activeIcon: Icon(Icons.library_books),
              label: 'Catalogues',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.store_outlined),
              activeIcon: Icon(Icons.store),
              label: 'Merchandising',
            ),
          ],
        ),
      ),
    );
  }
}

class DashboardTab extends StatefulWidget {
  const DashboardTab({super.key});

  @override
  State<DashboardTab> createState() => _DashboardTabState();
}

class _DashboardTabState extends State<DashboardTab> {
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Trigger initial data loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    if (_hasInitialized || !mounted) return;

    try {
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );
      final productProvider = Provider.of<ProduitProvider>(
        context,
        listen: false,
      );
      final orderProvider = Provider.of<CommandeProvider>(
        context,
        listen: false,
      );

      // Load data if not already loaded
      List<Future> loadingTasks = [];

      if (clientProvider.clients.isEmpty && !clientProvider.isLoading) {
        loadingTasks.add(clientProvider.loadClients());
      }
      if (productProvider.produits.isEmpty && !productProvider.isLoading) {
        loadingTasks.add(productProvider.chargerProduits());
      }
      if (orderProvider.commandes.isEmpty && !orderProvider.isLoading) {
        loadingTasks.add(orderProvider.chargerCommandes());
      }

      if (loadingTasks.isNotEmpty && mounted) {
        await Future.wait(loadingTasks);
      }

      if (mounted) {
        _hasInitialized = true;
      }
    } catch (e) {
      // Silently handle errors if widget is disposed
      if (mounted) {
        print('Error loading initial data: $e');
      }
    }
  }

  Future<void> _refreshData() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final productProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final orderProvider = Provider.of<CommandeProvider>(context, listen: false);

    await Future.wait([
      clientProvider.loadClients(),
      productProvider.chargerProduits(),
      orderProvider.chargerCommandes(),
    ]);
  }

  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: const Color(0xFFF8FAFC),
      child: SafeArea(
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
                ),
              ),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  final userEmail = authProvider.currentUser ?? '';
                  final userName =
                      userEmail.isNotEmpty
                          ? userEmail.split('@')[0].replaceAll('.', ' ')
                          : 'Utilisateur';

                  return Column(
                    children: [
                      // Logo
                      const VitaBrosseLogo(height: 40, showText: false),
                      const SizedBox(height: 16),

                      // User Avatar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            userName.isNotEmpty
                                ? userName[0].toUpperCase()
                                : 'U',
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF3B82F6),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // User Info
                      Text(
                        userName.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        userEmail,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // Menu Items
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Settings
                    Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF3B82F6,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.settings_outlined,
                            color: Color(0xFF3B82F6),
                            size: 20,
                          ),
                        ),
                        title: const Text(
                          'Paramètres',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF64748B),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to settings
                        },
                      ),
                    ),

                    const Spacer(),

                    // Logout Button
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Consumer<AuthProvider>(
                        builder: (context, authProvider, _) {
                          return ElevatedButton.icon(
                            onPressed: () async {
                              print('🔓 Logout button pressed');

                              // Close drawer first
                              Navigator.pop(context);

                              // Wait a bit for the drawer to close
                              await Future.delayed(
                                const Duration(milliseconds: 300),
                              );

                              // Check if context is still mounted
                              if (!context.mounted) {
                                print(
                                  '❌ Context no longer mounted after drawer close',
                                );
                                return;
                              }

                              print('🔍 Showing logout confirmation dialog');

                              final shouldLogout =
                                  await showDialog<bool>(
                                    context: context,
                                    builder:
                                        (dialogContext) => AlertDialog(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                          title: const Text(
                                            'Déconnexion',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w700,
                                              color: Color(0xFF1E293B),
                                            ),
                                          ),
                                          content: const Text(
                                            'Voulez-vous vraiment vous déconnecter ?',
                                            style: TextStyle(
                                              color: Color(0xFF64748B),
                                            ),
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed: () {
                                                print('❌ Logout cancelled');
                                                Navigator.of(
                                                  dialogContext,
                                                ).pop(false);
                                              },
                                              child: const Text(
                                                'Annuler',
                                                style: TextStyle(
                                                  color: Color(0xFF64748B),
                                                ),
                                              ),
                                            ),
                                            ElevatedButton(
                                              onPressed: () {
                                                print('✅ Logout confirmed');
                                                Navigator.of(
                                                  dialogContext,
                                                ).pop(true);
                                              },
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: const Color(
                                                  0xFFEF4444,
                                                ),
                                                foregroundColor: Colors.white,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                              ),
                                              child: const Text('Déconnexion'),
                                            ),
                                          ],
                                        ),
                                  ) ??
                                  false;

                              print(
                                '🔍 Dialog result: shouldLogout = $shouldLogout, context.mounted = ${context.mounted}',
                              );

                              if (shouldLogout && context.mounted) {
                                print('🚀 Starting logout process...');
                                try {
                                  // Show loading indicator
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder:
                                        (context) => const Center(
                                          child: CircularProgressIndicator(
                                            color: Color(0xFF10B981),
                                          ),
                                        ),
                                  );

                                  // Perform logout
                                  print('🔄 Calling authProvider.logout()...');
                                  await authProvider.logout();
                                  print('✅ authProvider.logout() completed');

                                  // Close loading dialog if still mounted
                                  if (context.mounted) {
                                    print('🔄 Closing loading dialog...');
                                    Navigator.of(context).pop();
                                  }

                                  // Navigate to login screen
                                  if (context.mounted) {
                                    print('🔄 Navigating to login screen...');
                                    Navigator.of(context).pushAndRemoveUntil(
                                      MaterialPageRoute(
                                        builder: (_) => const LoginScreen(),
                                      ),
                                      (route) =>
                                          false, // Remove all previous routes
                                    );
                                    print('✅ Navigation completed');
                                  }
                                } catch (e) {
                                  print('❌ Error during logout: $e');

                                  // Close loading dialog if still mounted
                                  if (context.mounted) {
                                    print(
                                      '🔄 Closing loading dialog after error...',
                                    );
                                    Navigator.of(context).pop();
                                  }

                                  // Show error message
                                  if (context.mounted) {
                                    print('🔄 Showing error message...');
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Erreur lors de la déconnexion: $e',
                                        ),
                                        backgroundColor: const Color(
                                          0xFFEF4444,
                                        ),
                                      ),
                                    );
                                  }
                                }
                              } else {
                                print(
                                  '❌ Logout cancelled or context not mounted',
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFEF4444),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            icon: const Icon(Icons.logout_rounded, size: 20),
                            label: const Text(
                              'Déconnexion',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: _buildDrawer(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight:
                  MediaQuery.of(context).size.width < 600 ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              leading: Builder(
                builder:
                    (context) => IconButton(
                      icon: const Icon(
                        Icons.menu,
                        color: Color(0xFF1E293B),
                        size: 28,
                      ),
                      tooltip: 'Menu',
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
              ),
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
                title: Row(
                  children: [
                    VitaBrosseLogo(
                      height: MediaQuery.of(context).size.width < 600 ? 24 : 28,
                      showText: false,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'VitaBrosse®',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                              fontSize:
                                  MediaQuery.of(context).size.width < 600
                                      ? 18
                                      : 20,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Gestion Professionnelle',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF6366F1).withValues(alpha: 0.05),
                        const Color(0xFF8B5CF6).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 40,
                        right: 20,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF6366F1).withValues(alpha: 0.1),
                                const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 60,
                        right: 60,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF8B5CF6).withValues(alpha: 0.15),
                                const Color(0xFF6366F1).withValues(alpha: 0.15),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(
                  MediaQuery.of(context).size.width < 480 ? 16.0 : 20.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bienvenue !',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF1F2937),
                        fontSize:
                            MediaQuery.of(context).size.width < 360
                                ? 20
                                : MediaQuery.of(context).size.width < 480
                                ? 22
                                : 24,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Voici un aperçu de votre activité commerciale',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.grey.shade600,
                        fontSize:
                            MediaQuery.of(context).size.width < 360
                                ? 13
                                : MediaQuery.of(context).size.width < 480
                                ? 14
                                : 16,
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                    ), // Statistics Cards - Responsive Layout
                    _buildResponsiveStatCards(context),

                    SizedBox(
                      height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                    ),

                    // Revenue Chart Section
                    SectionHeader(
                      title: 'Analyse des revenus',
                      subtitle: 'Répartition du chiffre d\'affaires par client',
                    ),

                    const SizedBox(height: 16),

                    ProfessionalCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: const RevenueChart(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    Widget valueWidget,
  ) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildCardContent(String value, String subtitle) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isVerySmallScreen = screenWidth < 360;
        final isSmallScreen = screenWidth < 480;

        // Ajuster les tailles de police selon l'écran
        final valueFontSize =
            isVerySmallScreen ? 14.0 : (isSmallScreen ? 16.0 : 20.0);
        final subtitleFontSize =
            isVerySmallScreen ? 9.0 : (isSmallScreen ? 10.0 : 11.0);

        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment:
              MainAxisAlignment.center, // Centre le contenu verticalement
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: valueFontSize,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 1),
            Flexible(
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: subtitleFontSize,
                  color: Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildResponsiveStatCards(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 360; // iPhone SE et similaires
    final isSmallScreen = screenWidth < 480; // Petits téléphones
    final isMediumScreen = screenWidth < 600; // Grands téléphones

    final cards = [
      _buildStatCard(
        context,
        'Clients',
        Icons.people_outline,
        const Color(0xFF3B82F6),
        Consumer<FirebaseClientProvider>(
          builder: (context, clientProvider, child) {
            if (clientProvider.isLoading) {
              return const ModernLoadingIndicator(size: 16);
            }

            // Get statistics directly from the provider
            return FutureBuilder<Map<String, dynamic>>(
              future: clientProvider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const ModernLoadingIndicator(size: 16);
                }
                if (snapshot.hasError) {
                  return _buildCardContent('0', 'Erreur');
                }
                if (snapshot.hasData && snapshot.data != null) {
                  final data = snapshot.data!;
                  return _buildCardContent(
                    '${data['totalClients'] ?? 0}',
                    'Total',
                  );
                }
                return _buildCardContent('0', 'Total');
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Produits',
        Icons.inventory_2_outlined,
        const Color(0xFF10B981),
        Consumer<ProduitProvider>(
          builder: (context, productProvider, child) {
            if (productProvider.isLoading) {
              return const ModernLoadingIndicator(size: 16);
            }

            return FutureBuilder<Map<String, dynamic>>(
              future: productProvider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const ModernLoadingIndicator(size: 16);
                }
                if (snapshot.hasError) {
                  return _buildCardContent('0', 'En stock');
                }
                if (snapshot.hasData && snapshot.data != null) {
                  return _buildCardContent(
                    '${snapshot.data!['nombreTotal'] ?? 0}',
                    'En stock',
                  );
                }
                return _buildCardContent('0', 'En stock');
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Commandes',
        Icons.shopping_cart_outlined,
        const Color(0xFFF59E0B),
        Consumer<CommandeProvider>(
          builder: (context, orderProvider, child) {
            if (orderProvider.isLoading) {
              return const ModernLoadingIndicator(size: 16);
            }

            return FutureBuilder<Map<String, dynamic>>(
              future: orderProvider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const ModernLoadingIndicator(size: 16);
                }
                if (snapshot.hasError) {
                  return _buildCardContent('0', 'Total');
                }
                if (snapshot.hasData && snapshot.data != null) {
                  final data = snapshot.data!;
                  return _buildCardContent('${data['total'] ?? 0}', 'Total');
                }
                return _buildCardContent('0', 'Total');
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Revenus',
        Icons.attach_money,
        const Color(0xFF8B5CF6),
        Consumer<CommandeProvider>(
          builder: (context, orderProvider, child) {
            if (orderProvider.isLoading) {
              return const ModernLoadingIndicator(size: 16);
            }

            return FutureBuilder<Map<String, dynamic>>(
              future: orderProvider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const ModernLoadingIndicator(size: 16);
                }
                if (snapshot.hasError) {
                  return _buildCardContent('0.0k DT', 'Total');
                }
                if (snapshot.hasData && snapshot.data != null) {
                  final data = snapshot.data!;
                  final ca = (data['chiffreAffaires'] as double?) ?? 0.0;
                  return _buildCardContent(
                    '${(ca / 1000).toStringAsFixed(1)}k DT',
                    'Total',
                  );
                }
                return _buildCardContent('0.0k DT', 'Total');
              },
            );
          },
        ),
      ),
    ];

    if (isMediumScreen) {
      // Sur écrans de téléphone : défilement horizontal optimisé
      final cardWidth =
          isVerySmallScreen ? 120.0 : (isSmallScreen ? 140.0 : 160.0);
      final cardHeight =
          isVerySmallScreen
              ? 110.0
              : (isSmallScreen
                  ? 120.0
                  : 130.0); // Augmenté pour éviter l'overflow

      return SizedBox(
        height: cardHeight,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: cards.length,
          separatorBuilder:
              (context, index) => SizedBox(width: isSmallScreen ? 8 : 12),
          itemBuilder: (context, index) {
            return SizedBox(width: cardWidth, child: cards[index]);
          },
        ),
      );
    } else {
      // Sur tablettes et écrans plus grands : grille responsive
      final crossAxisCount = screenWidth > 800 ? 4 : 2;
      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio:
            screenWidth > 800 ? 2.2 : 2.5, // Ajusté selon la largeur
        children: cards,
      );
    }
  }
}
