import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:math' as math;
import '../../models/rapport_merchandising.dart';
import '../../providers/rapport_provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/google_drive_service.dart';

class CreerRapportScreen extends StatefulWidget {
  final String? missionId;
  final RapportMerchandising? rapport;

  const CreerRapportScreen({super.key, this.missionId, this.rapport});

  @override
  State<CreerRapportScreen> createState() => _CreerRapportScreenState();
}

class _CreerRapportScreenState extends State<CreerRapportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titreController = TextEditingController();
  final _observationsController = TextEditingController();
  final _recommandationsController = TextEditingController();

  String? _missionSelectionnee;
  List<File> _photos = [];
  List<String> _photoUrls = []; // Store Google Drive URLs
  bool _isLoading = false;
  bool _uploadingPhoto = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _missionSelectionnee = widget.missionId;

    if (widget.rapport != null) {
      _titreController.text = widget.rapport!.titre;
      _observationsController.text = widget.rapport!.observations;
      _recommandationsController.text = widget.rapport!.recommandations ?? '';
      _missionSelectionnee = widget.rapport!.missionId;
      // Load existing photo URLs, filtering out local file paths
      _photoUrls =
          widget.rapport!.photos
              .where((photo) => photo.startsWith('https://drive.google.com/'))
              .toList();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final provider = Provider.of<MissionProvider>(context, listen: false);
    provider.chargerMissions();
  }

  @override
  void dispose() {
    _titreController.dispose();
    _observationsController.dispose();
    _recommandationsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.rapport == null ? 'Nouveau rapport' : 'Modifier le rapport',
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _sauvegarderBrouillon,
            child: const Text(
              'Brouillon',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sélection de mission
              Consumer<MissionProvider>(
                builder: (context, provider, child) {
                  return DropdownButtonFormField<String>(
                    value: _missionSelectionnee,
                    decoration: const InputDecoration(
                      labelText: 'Mission',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        provider.missions.map((mission) {
                          return DropdownMenuItem(
                            value: mission.id,
                            child: Text(
                              '${mission.titre} - ${mission.clientNom}',
                            ),
                          );
                        }).toList(),
                    onChanged:
                        widget.missionId == null
                            ? (value) {
                              setState(() {
                                _missionSelectionnee = value;
                              });
                            }
                            : null,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Veuillez sélectionner une mission';
                      }
                      return null;
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Titre
              TextFormField(
                controller: _titreController,
                decoration: const InputDecoration(
                  labelText: 'Titre du rapport',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir un titre';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Observations
              TextFormField(
                controller: _observationsController,
                maxLines: 4,
                decoration: const InputDecoration(
                  labelText: 'Observations',
                  hintText: 'Décrivez ce que vous avez observé...',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir vos observations';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Recommandations
              TextFormField(
                controller: _recommandationsController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Recommandations (optionnel)',
                  hintText: 'Vos recommandations pour améliorer...',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 24),

              // Photos
              const Text(
                'Photos',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),

              // Boutons pour ajouter des photos
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _ajouterPhoto(ImageSource.camera),
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Appareil photo'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _ajouterPhoto(ImageSource.gallery),
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galerie'),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Grille des photos
              if (_photos.isNotEmpty ||
                  _photoUrls.isNotEmpty ||
                  _uploadingPhoto) ...[
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount:
                      math.max(_photos.length, _photoUrls.length) +
                      (_uploadingPhoto ? 1 : 0),
                  itemBuilder: (context, index) {
                    final maxCount = math.max(
                      _photos.length,
                      _photoUrls.length,
                    );
                    // Show upload progress for the last item if uploading
                    if (_uploadingPhoto && index == maxCount) {
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey[200],
                        ),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFF10B981),
                                ),
                              ),
                              SizedBox(height: 8),
                              Text('Upload...', style: TextStyle(fontSize: 12)),
                            ],
                          ),
                        ),
                      );
                    }

                    // Determine if we have a local file or need to show URL
                    final hasLocalFile = index < _photos.length;
                    final hasUrl =
                        index < _photoUrls.length &&
                        _photoUrls[index].isNotEmpty;

                    return Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.grey[200],
                          ),
                          child:
                              hasLocalFile
                                  ? Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: FileImage(_photos[index]),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  )
                                  : hasUrl
                                  ? Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.image,
                                            color: Colors.grey,
                                            size: 32,
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'Image\nstockée',
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                  : const SizedBox(),
                        ),
                        // Upload success indicator
                        if (index < _photoUrls.length &&
                            _photoUrls[index].isNotEmpty)
                          Positioned(
                            top: 4,
                            left: 4,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Color(0xFF10B981),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.cloud_done,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                          ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _supprimerPhoto(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 24),
              ],

              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _envoyerRapport,
                      child:
                          _isLoading
                              ? const CircularProgressIndicator()
                              : const Text('Envoyer'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _ajouterPhoto(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(source: source);
      if (image != null) {
        setState(() {
          _uploadingPhoto = true;
        });

        // Upload to Google Drive
        final file = File(image.path);
        final fileName =
            'rapport_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';

        try {
          final driveUrl = await GoogleDriveService.uploadImage(file, fileName);

          if (mounted) {
            setState(() {
              _photos.add(file); // Keep local file for preview
              _photoUrls.add(driveUrl); // Store Google Drive URL
              _uploadingPhoto = false;
            });
          }
        } catch (uploadError) {
          if (mounted) {
            setState(() {
              _uploadingPhoto = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Erreur lors de l\'upload: $uploadError'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _uploadingPhoto = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ajout de la photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _supprimerPhoto(int index) {
    // Delete from Google Drive if URL exists
    if (index < _photoUrls.length) {
      GoogleDriveService.deleteImage(_photoUrls[index]);
    }

    setState(() {
      if (index < _photos.length) {
        _photos.removeAt(index);
      }
      if (index < _photoUrls.length) {
        _photoUrls.removeAt(index);
      }
    });
  }

  Future<void> _sauvegarderBrouillon() async {
    await _sauvegarder(statut: 'brouillon');
  }

  Future<void> _envoyerRapport() async {
    if (!_formKey.currentState!.validate()) return;
    await _sauvegarder(statut: 'envoye');
  }

  Future<void> _sauvegarder({required String statut}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Get the real merchandiser ID from auth provider
      final merchandiserId = authProvider.currentUserId;
      if (merchandiserId == null) {
        throw Exception('Utilisateur non connecté');
      }

      final rapport = RapportMerchandising(
        id: widget.rapport?.id,
        missionId: _missionSelectionnee!,
        merchandiserId: merchandiserId, // Use real merchandiser ID
        titre: _titreController.text.trim(),
        observations: _observationsController.text.trim(),
        recommandations:
            _recommandationsController.text.trim().isEmpty
                ? null
                : _recommandationsController.text.trim(),
        dateCreation: widget.rapport?.dateCreation ?? DateTime.now(),
        dateModification: DateTime.now(),
        statut: statut,
        photos: _photoUrls, // Use Google Drive URLs instead of file paths
      );

      bool success;
      if (widget.rapport == null) {
        success = await provider.creerRapportMerchandising(rapport);
      } else {
        success = await provider.modifierRapportMerchandising(rapport);
      }

      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              statut == 'brouillon'
                  ? 'Rapport sauvegardé en brouillon'
                  : 'Rapport envoyé avec succès',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
