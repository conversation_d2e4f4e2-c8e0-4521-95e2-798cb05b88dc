import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/mission.dart';
import '../../providers/mission_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'creer_mission_calendrier_screen.dart';
import 'creer_rapport_screen.dart';

class MissionsDateScreen extends StatefulWidget {
  final DateTime selectedDate;

  const MissionsDateScreen({super.key, required this.selectedDate});

  @override
  State<MissionsDateScreen> createState() => _MissionsDateScreenState();
}

class _MissionsDateScreenState extends State<MissionsDateScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    missionProvider.chargerMissions();
  }

  String _formatDate(DateTime date) {
    const months = [
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;

    return Scaffold(
      body: Consumer<MissionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(color: Color(0xFF10B981)),
              ),
            );
          }

          final missionsDate =
              provider.missions.where((mission) {
                return _isSameDay(mission.dateEcheance, widget.selectedDate);
              }).toList();

          return RefreshIndicator(
            onRefresh: () async => _chargerMissions(),
            color: const Color(0xFF10B981),
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight: isSmallScreen ? 120 : 140,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.white,
                  surfaceTintColor: Colors.white,
                  elevation: 0,
                  scrolledUnderElevation: 2,
                  leading: Builder(
                    builder:
                        (context) => IconButton(
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Color(0xFF1E293B),
                            size: 28,
                          ),
                          tooltip: 'Retour',
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                  ),
                  actions: [
                    Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        if (authProvider.userType == 'commercial') {
                          return IconButton(
                            icon: const Icon(
                              Icons.add_circle,
                              color: Color(0xFF10B981),
                              size: 28,
                            ),
                            tooltip: 'Ajouter une mission',
                            onPressed: () => _creerNouvelleMission(context),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.refresh,
                        color: Color(0xFF1E293B),
                        size: 28,
                      ),
                      tooltip: 'Actualiser',
                      onPressed: _chargerMissions,
                    ),
                    const SizedBox(width: 8),
                  ],
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
                    title: Row(
                      children: [
                        VitaBrosseLogo(
                          height: isSmallScreen ? 24 : 28,
                          showText: false,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Missions du ${_formatDate(widget.selectedDate)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  color: const Color(0xFF1F2937),
                                  fontSize: isSmallScreen ? 16 : 18,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                '${missionsDate.length} mission${missionsDate.length > 1 ? 's' : ''}',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey.shade600,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            const Color(0xFF10B981).withValues(alpha: 0.08),
                            const Color(0xFF059669).withValues(alpha: 0.08),
                          ],
                          stops: const [0.0, 0.7, 1.0],
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            top: isSmallScreen ? 20 : 40,
                            right: isSmallScreen ? 10 : 20,
                            child: Container(
                              width: isSmallScreen ? 60 : 100,
                              height: isSmallScreen ? 60 : 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(
                                      0xFF10B981,
                                    ).withValues(alpha: 0.15),
                                    const Color(
                                      0xFF059669,
                                    ).withValues(alpha: 0.1),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: isSmallScreen ? 30 : 60,
                            right: isSmallScreen ? 20 : 40,
                            child: Container(
                              width: isSmallScreen ? 40 : 60,
                              height: isSmallScreen ? 40 : 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [
                                    const Color(
                                      0xFF059669,
                                    ).withValues(alpha: 0.2),
                                    const Color(
                                      0xFF10B981,
                                    ).withValues(alpha: 0.15),
                                  ],
                                ),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.assignment,
                                  size: isSmallScreen ? 20 : 28,
                                  color: const Color(0xFF059669),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                  sliver:
                      missionsDate.isEmpty
                          ? SliverFillRemaining(
                            child: _buildEmptyState(context, isSmallScreen),
                          )
                          : SliverList(
                            delegate: SliverChildBuilderDelegate((
                              context,
                              index,
                            ) {
                              final mission = missionsDate[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: _buildMissionCard(
                                  mission,
                                  isSmallScreen,
                                ),
                              );
                            }, childCount: missionsDate.length),
                          ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isSmallScreen) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: isSmallScreen ? 80 : 120,
            height: isSmallScreen ? 80 : 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF10B981).withValues(alpha: 0.1),
                  const Color(0xFF059669).withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Icon(
              Icons.event_available,
              size: isSmallScreen ? 40 : 60,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Aucune mission pour ce jour',
            style: TextStyle(
              fontSize: isSmallScreen ? 18 : 22,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Il n\'y a pas de missions planifiées\npour le ${_formatDate(widget.selectedDate)}',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: isSmallScreen ? 14 : 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 32),
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.userType == 'commercial') {
                return ElevatedButton.icon(
                  onPressed: () => _creerNouvelleMission(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Créer une mission'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF10B981),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 24 : 32,
                      vertical: isSmallScreen ? 12 : 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMissionCard(Mission mission, bool isSmallScreen) {
    return GestureDetector(
      onTap: () => _gererClicMission(mission),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header avec priorité et statut
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getPrioriteColor(
                  mission.priorite,
                ).withValues(alpha: 0.05),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getPrioriteColor(
                        mission.priorite,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.flag,
                          size: 14,
                          color: _getPrioriteColor(mission.priorite),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getPrioriteText(mission.priorite),
                          style: TextStyle(
                            color: _getPrioriteColor(mission.priorite),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatutColor(
                        mission.statut,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getStatutText(mission.statut),
                      style: TextStyle(
                        color: _getStatutColor(mission.statut),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Contenu principal
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    mission.titre,
                    style: TextStyle(
                      fontSize: isSmallScreen ? 16 : 18,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    mission.description,
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 15,
                      color: Colors.grey[600],
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),

                  // Informations client et merchandiser
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          Icons.person_outline,
                          mission.clientNom,
                          const Color(0xFF3B82F6),
                          isSmallScreen,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            return _buildInfoChip(
                              Icons.store_outlined,
                              authProvider.userType == 'commercial'
                                  ? 'Merchandiser'
                                  : 'Ma mission',
                              const Color(0xFF10B981),
                              isSmallScreen,
                            );
                          },
                        ),
                      ),
                    ],
                  ),

                  if (mission.taches.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Text(
                      'Tâches (${mission.taches.length})',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    ...mission.taches
                        .take(2)
                        .map(
                          (tache) => Padding(
                            padding: const EdgeInsets.only(bottom: 2),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  size: 14,
                                  color: Colors.grey[500],
                                ),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    tache,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    if (mission.taches.length > 2)
                      Text(
                        '... et ${mission.taches.length - 2} autre${mission.taches.length - 2 > 1 ? 's' : ''}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    IconData icon,
    String text,
    Color color,
    bool isSmallScreen,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSmallScreen ? 11 : 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for colors and text
  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return const Color(0xFFEF4444);
      case 'haute':
        return const Color(0xFFF59E0B);
      case 'normale':
        return const Color(0xFF3B82F6);
      case 'faible':
        return const Color(0xFF10B981);
      default:
        return const Color(0xFF6B7280);
    }
  }

  String _getPrioriteText(String priorite) {
    switch (priorite) {
      case 'urgente':
        return 'URGENTE';
      case 'haute':
        return 'HAUTE';
      case 'normale':
        return 'NORMALE';
      case 'faible':
        return 'FAIBLE';
      default:
        return 'NORMALE';
    }
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'terminee':
        return const Color(0xFF10B981);
      case 'en_cours':
        return const Color(0xFFF59E0B);
      case 'en_attente':
        return const Color(0xFF3B82F6);
      case 'annulee':
        return const Color(0xFFEF4444);
      default:
        return const Color(0xFF6B7280);
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'terminee':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      case 'annulee':
        return 'Annulée';
      default:
        return 'En attente';
    }
  }

  // Action methods
  void _gererClicMission(Mission mission) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.userType == 'merchandiser') {
      // Pour les merchandisers, naviguer vers la création de rapport
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CreerRapportScreen(missionId: mission.id),
        ),
      );
    } else {
      // Pour les commerciaux, afficher les détails ou permettre l'édition
      _afficherDetailsMission(mission);
    }
  }

  void _afficherDetailsMission(Mission mission) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  height: 4,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                mission.titre,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                            ),
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                if (authProvider.userType == 'commercial') {
                                  return IconButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                      _modifierMission(mission);
                                    },
                                    icon: const Icon(Icons.edit),
                                    color: const Color(0xFF10B981),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          mission.description,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 24),
                        _buildDetailRow('Client', mission.clientNom),
                        _buildDetailRow(
                          'Priorité',
                          _getPrioriteText(mission.priorite),
                        ),
                        _buildDetailRow(
                          'Statut',
                          _getStatutText(mission.statut),
                        ),
                        if (mission.notes != null) ...[
                          const SizedBox(height: 16),
                          const Text(
                            'Notes',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            mission.notes!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                              height: 1.5,
                            ),
                          ),
                        ],
                        if (mission.taches.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          const Text(
                            'Tâches à effectuer',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 12),
                          ...mission.taches.map(
                            (tache) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.check_circle_outline,
                                    size: 20,
                                    color: Colors.grey[500],
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      tache,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[700],
                                        height: 1.4,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF6B7280),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Color(0xFF1F2937)),
            ),
          ),
        ],
      ),
    );
  }

  void _modifierMission(Mission mission) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder:
            (context) => CreerMissionCalendrierScreen(
              selectedDate: mission.dateEcheance,
              missionToEdit: mission,
            ),
      ),
    );

    if (result == true) {
      _chargerMissions();
    }
  }

  void _creerNouvelleMission(BuildContext context) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                CreerMissionCalendrierScreen(selectedDate: widget.selectedDate),
      ),
    );

    if (result == true) {
      _chargerMissions();
    }
  }
}
