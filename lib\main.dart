import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_client_provider.dart';
import 'providers/produit_provider.dart';
import 'providers/commande_provider.dart';
import 'providers/merchandiser_provider.dart';
import 'providers/parcours_provider.dart';
import 'providers/catalogue_provider.dart';
import 'providers/tache_merchandising_provider.dart';
import 'providers/mission_provider.dart';
import 'providers/rapport_provider.dart';
import 'providers/devis_provider.dart';
import 'screens/auth/login_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/firebase_auth_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Launch app immediately with splash screen
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        // Add splash screen colors
        splashColor: const Color(0xFF10B981),
        primaryColor: const Color(0xFF10B981),
      ),
      home: const SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize Firebase
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Initialize Firestore
      await FirebaseService.initializeFirestore();

      // Wait a minimum time for smooth UX (reduced for faster startup)
      await Future.delayed(const Duration(milliseconds: 800));

      // Navigate to main app
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainApp()),
        );
      }
    } catch (e) {
      print('Erreur lors de l\'initialisation Firebase: $e');
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => MyErrorApp(error: e.toString()),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: const Color(0xFF10B981),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF10B981).withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(Icons.business, size: 60, color: Colors.white),
            ),
            const SizedBox(height: 30),

            // App name
            const Text(
              'VitaBrosse Pro',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 10),

            // Subtitle
            const Text(
              'Solution professionnelle',
              style: TextStyle(fontSize: 16, color: Color(0xFF6B7280)),
            ),
            const SizedBox(height: 50),

            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
              strokeWidth: 3,
            ),
            const SizedBox(height: 20),

            // Loading text
            const Text(
              'Initialisation...',
              style: TextStyle(fontSize: 14, color: Color(0xFF6B7280)),
            ),
          ],
        ),
      ),
    );
  }
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core providers - loaded immediately
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseAuthProvider()),

        // Business providers - created lazily
        ChangeNotifierProvider(create: (context) => FirebaseClientProvider()),
        ChangeNotifierProvider(create: (context) => ProduitProvider()),
        ChangeNotifierProvider(create: (context) => CommandeProvider()),
        ChangeNotifierProvider(create: (context) => DevisProvider()),
        ChangeNotifierProvider(create: (context) => MerchandiserProvider()),
        ChangeNotifierProvider(create: (context) => ParcoursProvider()),
        ChangeNotifierProvider(create: (context) => CatalogueProvider()),
        ChangeNotifierProvider(
          create: (context) => TacheMerchandisingProvider(),
        ),
        ChangeNotifierProvider(create: (context) => MissionProvider()),
        ChangeNotifierProvider(create: (context) => RapportProvider()),
      ],
      child: MaterialApp(
        title: 'VitaBrosse Pro',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          primaryColor: const Color(0xFF10B981),
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF10B981),
            brightness: Brightness.light,
          ),
        ),
        home: const LoginScreen(),
      ),
    );
  }
}

class MyErrorApp extends StatelessWidget {
  final String error;

  const MyErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro - Erreur',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.red, useMaterial3: true),
      home: Scaffold(
        appBar: AppBar(
          title: Text('Erreur d\'initialisation'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 100, color: Colors.red),
                SizedBox(height: 20),
                Text(
                  'VitaBrosse Pro',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                Text(
                  'Erreur lors de l\'initialisation Firebase:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.red),
                ),
                SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // Relancer l'application
                    main();
                  },
                  child: Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
