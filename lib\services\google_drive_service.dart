import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

class GoogleDriveService {
  static const String _serviceAccountEmail =
      '<EMAIL>';
  static const String _apiKey = 'AIzaSyCLP_hAgpR0WAN0_t7DqBF9XPS1yALpkvk';

  // VitaBrosse folder ID in Google Drive (you'll need to create this folder)
  static const String _vitaBrosseFolderId =
      '1BCD123XYZ'; // Replace with actual folder ID

  static bool _initialized = false;

  /// Initialize the Google Drive service
  static Future<void> initialize() async {
    try {
      print('🔧 Initializing Google Drive service...');
      _initialized = true;
      print('✅ Google Drive service initialized');
    } catch (e) {
      print('❌ Error initializing Google Drive service: $e');
      _initialized = false;
      rethrow;
    }
  }

  /// Upload an image file to Google Drive and return the public link
  static Future<String> uploadImage(File imageFile, String fileName) async {
    try {
      print('📤 Uploading image to Google Drive: $fileName');

      // For now, we'll simulate the upload and return a placeholder URL
      // In production, you would implement the actual upload

      // Read the file
      final bytes = await imageFile.readAsBytes();
      print('📁 File size: ${bytes.length} bytes');

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 2));

      // Generate a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = '${timestamp}_$fileName';

      // For now, return a placeholder URL
      // In production, this would be the actual Google Drive file URL
      final driveUrl =
          'https://drive.google.com/file/d/placeholder_${uniqueFileName}/view';

      print('✅ Image uploaded successfully: $driveUrl');
      return driveUrl;
    } catch (e) {
      print('❌ Error uploading image to Google Drive: $e');
      throw Exception('Failed to upload image to Google Drive: $e');
    }
  }

  /// Upload an image from bytes to Google Drive
  static Future<String> uploadImageFromBytes(
    Uint8List imageBytes,
    String fileName,
    String mimeType,
  ) async {
    try {
      print('📤 Uploading image bytes to Google Drive: $fileName');

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 1));

      // Generate a unique filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = '${timestamp}_$fileName';

      // For now, return a placeholder URL
      final driveUrl =
          'https://drive.google.com/file/d/placeholder_${uniqueFileName}/view';

      print('✅ Image bytes uploaded successfully: $driveUrl');
      return driveUrl;
    } catch (e) {
      print('❌ Error uploading image bytes to Google Drive: $e');
      throw Exception('Failed to upload image bytes to Google Drive: $e');
    }
  }

  /// Get a public viewable URL for a Google Drive file
  static String getPublicUrl(String fileId) {
    return 'https://drive.google.com/file/d/$fileId/view';
  }

  /// Extract file ID from Google Drive URL
  static String? extractFileId(String driveUrl) {
    final regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
    final match = regex.firstMatch(driveUrl);
    return match?.group(1);
  }

  /// Delete a file from Google Drive
  static Future<bool> deleteImage(String driveUrl) async {
    try {
      final fileId = extractFileId(driveUrl);
      if (fileId == null) {
        print('❌ Could not extract file ID from URL: $driveUrl');
        return false;
      }

      print('🗑️ Deleting image from Google Drive: $fileId');

      // Simulate deletion
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ Image deleted successfully');
      return true;
    } catch (e) {
      print('❌ Error deleting image from Google Drive: $e');
      return false;
    }
  }

  /// Dispose resources
  static void dispose() {
    _initialized = false;
  }
}

// Extension to get file extension from path
extension FileExtension on File {
  String get extension {
    return path.split('.').last.toLowerCase();
  }

  String get mimeType {
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}
